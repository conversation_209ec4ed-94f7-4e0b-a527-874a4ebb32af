defmodule MqttableWeb.SubscriptionModalComponent do
  @moduledoc """
  LiveComponent for handling MQTT topic subscription functionality.
  This component manages the subscription form and related operations.
  """
  use MqttableWeb, :live_component
  import Phoenix.LiveView, only: [put_flash: 3]
  import MqttableWeb.Shared.MessageFormComponents

  @impl true
  def render(assigns) do
    ~H"""
    <div class="modal modal-open">
      <div class="modal-box subscription-modal-box max-w-2xl w-full">
        <h3 class="font-bold text-lg flex items-center">
          <.icon name="hero-rss" class="size-5 mr-2" />
          <%= if @edit_mode do %>
            Edit Subscription
          <% else %>
            New Subscription
          <% end %>
        </h3>

        <.form for={@form} phx-submit="save" phx-target={@myself} class="mt-4">
          <!-- Hidden Index Field for Edit Mode -->
          <%= if @edit_mode do %>
            <input type="hidden" name="index" value={@form["index"]} />
          <% end %>
          
    <!-- Topic Field -->
          <div class="form-control w-full mb-4">
            <label class="label">
              <span class="label-text font-medium">
                Topic <span class="text-error">*</span>
              </span>
            </label>
            <input
              type="text"
              name="topic"
              value={@form["topic"]}
              placeholder="Enter topic (e.g., 'device/+/status')"
              class="input input-bordered w-full"
              required
            />
          </div>
          
    <!-- QoS and Retain Handling Row -->
          <div class="grid grid-cols-2 gap-4 mb-4">
            <!-- QoS Field -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">
                  QoS <span class="text-error">*</span>
                </span>
              </label>
              <.qos_selection form={@form} myself={@myself} />
            </div>
            
    <!-- Retain Handling Field (only for MQTT 5.0) -->
            <%= if show_mqtt5_options?(assigns) do %>
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Retain Handling</span>
                </label>
                <.retain_handling_selection form={@form} myself={@myself} />
              </div>
            <% end %>
          </div>
          
    <!-- MQTT 5.0 Specific Options -->
          <%= if show_mqtt5_options?(assigns) do %>
            <!-- Flags in one row -->
            <div class="form-control w-full mb-4">
              <div class="grid grid-cols-2 gap-4">
                <!-- No Local Flag -->
                <div class="flex items-center gap-2">
                  <span class="text-gray-700 font-medium">No Local</span>
                  <input
                    type="checkbox"
                    name="nl"
                    checked={@form["nl"] == 1}
                    class="toggle toggle-primary"
                  />
                  <span
                    class="label-text-alt tooltip"
                    data-tip="If enabled, the server will not forward publications to a client that has been published by itself"
                  >
                    <.icon name="hero-information-circle" class="size-4" />
                  </span>
                </div>
                
    <!-- Retain as Published Flag -->
                <div class="flex items-center gap-2">
                  <span class="text-gray-700 font-medium">Retain as Published</span>
                  <input
                    type="checkbox"
                    name="rap"
                    checked={@form["rap"] == 1}
                    class="toggle toggle-primary"
                  />
                  <span
                    class="label-text-alt tooltip"
                    data-tip="If enabled, the server will keep the retain flag when forwarding messages"
                  >
                    <.icon name="hero-information-circle" class="size-4" />
                  </span>
                </div>
              </div>
            </div>
            
    <!-- Subscription Identifier Field -->
            <div class="form-control w-full mb-4">
              <label class="label">
                <span class="label-text font-medium">Subscription Identifier</span>
                <span
                  class="label-text-alt tooltip"
                  data-tip="Optional positive integer identifier for this subscription"
                >
                  <.icon name="hero-information-circle" class="size-4" />
                </span>
              </label>
              <input
                type="number"
                name="sub_id"
                value={@form["sub_id"]}
                placeholder="Optional"
                class="input input-bordered w-full"
                min="1"
                step="1"
              />
            </div>
          <% end %>
          
    <!-- Client Selection -->
          <div class="form-control w-full mb-4">
            <label class="label">
              <span class="label-text font-medium">
                Client <span class="text-error">*</span>
              </span>
            </label>
            <%= if @pre_selected_client do %>
              <input type="hidden" name="client_id" value={@form["client_id"]} />
              <div class="input input-bordered w-full flex items-center">
                <%= for conn <- @connected_clients do %>
                  <%= if conn.client_id == @form["client_id"] do %>
                    <span>{conn.name} ({conn.client_id})</span>
                  <% end %>
                <% end %>
              </div>
            <% else %>
              <select
                name="client_id"
                class="select select-bordered w-full"
                required
                phx-change="client_changed"
                phx-target={@myself}
              >
                <option value="" disabled selected={@form["client_id"] == ""}>Select a client</option>
                <%= for conn <- @connected_clients do %>
                  <option value={conn.client_id} selected={@form["client_id"] == conn.client_id}>
                    {conn.name} ({conn.client_id}) - MQTT {conn.mqtt_version || "5.0"}
                  </option>
                <% end %>
              </select>
            <% end %>
            <%= if length(@connected_clients) == 0 do %>
              <p class="text-sm text-error mt-1">
                No connected clients available. Please connect a client first.
              </p>
            <% end %>
          </div>
          
    <!-- Action Buttons -->
          <div class="modal-action">
            <%= if @edit_mode do %>
              <button
                type="button"
                phx-click="delete"
                phx-target={@myself}
                class="btn btn-ghost text-error"
              >
                <.icon name="hero-trash" class="h-4 w-4 mr-1" /> Delete
              </button>
            <% end %>
            <div class="flex space-x-2 ml-auto">
              <button type="button" phx-click="cancel" phx-target={@myself} class="btn btn-ghost">
                Cancel
              </button>
              <button type="submit" class="btn btn-primary" disabled={length(@connected_clients) == 0}>
                Confirm
              </button>
            </div>
          </div>
        </.form>
      </div>
      <div class="modal-backdrop" phx-click="cancel" phx-target={@myself}></div>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    # Get connected clients from the active connection set
    connected_clients =
      if assigns.active_connection_set do
        connections = Map.get(assigns.active_connection_set, :connections, [])
        Enum.filter(connections, fn conn -> Map.get(conn, :status) == "connected" end)
      else
        []
      end

    # Get pre-selected client ID if provided
    pre_selected_client_id = Map.get(assigns, :pre_selected_client_id)

    # Check if we're in edit mode
    edit_mode = Map.get(assigns, :edit_mode, false)

    # Initialize the form with default values or edit values
    form =
      if edit_mode do
        # 保持 nl 和 rap 的原始值，不转换为布尔值
        nl_value = assigns.nl || 0
        rap_value = assigns.rap || 0

        # Handle subscription identifier properly
        sub_id_value =
          case assigns.sub_id do
            nil -> ""
            0 -> ""
            val when is_integer(val) and val > 0 -> Integer.to_string(val)
            val when is_binary(val) -> val
            _ -> ""
          end

        # Get the index value if available
        index_value = Map.get(assigns, :index, "")

        # Use the provided values for editing
        %{
          "topic" => assigns.topic || "",
          "qos" => assigns.qos || 0,
          "nl" => nl_value,
          "rap" => rap_value,
          "rh" => assigns.rh || 0,
          "sub_id" => sub_id_value,
          "client_id" => assigns.client_id || "",
          "index" => index_value
        }
      else
        # Default values for new subscription
        %{
          "topic" => "",
          "qos" => 0,
          "nl" => false,
          "rap" => false,
          "rh" => 0,
          "sub_id" => "",
          "client_id" => pre_selected_client_id || "",
          "index" => ""
        }
      end

    socket =
      socket
      |> assign(assigns)
      |> assign(:connected_clients, connected_clients)
      |> assign(:form, form)
      |> assign(:edit_mode, edit_mode)
      |> assign(:pre_selected_client, pre_selected_client_id != nil || edit_mode)

    {:ok, socket}
  end

  # Helper function to determine if MQTT 5.0 options should be shown
  defp show_mqtt5_options?(assigns) do
    selected_client_id = assigns.form["client_id"]

    if selected_client_id && selected_client_id != "" do
      # Find the selected client and check its MQTT version
      selected_client =
        Enum.find(assigns.connected_clients, fn conn ->
          conn.client_id == selected_client_id
        end)

      case selected_client do
        %{mqtt_version: "5.0"} -> true
        %{mqtt_version: version} when version in ["3.1", "3.1.1"] -> false
        # Default to MQTT 5.0 if version is not specified
        _ -> true
      end
    else
      # Default to showing MQTT 5.0 options if no client is selected
      true
    end
  end

  @impl true
  def handle_event("client_changed", %{"client_id" => client_id}, socket) do
    # Update the form with the new client_id
    form = Map.put(socket.assigns.form, "client_id", client_id)
    socket = assign(socket, :form, form)
    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel", _params, socket) do
    # Send a message to the parent to close the modal
    send(self(), {:close_subscription_modal})
    {:noreply, socket}
  end

  @impl true
  def handle_event("delete", _params, socket) do
    # Only allow delete in edit mode
    if socket.assigns.edit_mode do
      # Get the client_id and topic from the form
      client_id = socket.assigns.form["client_id"] || socket.assigns.pre_selected_client_id
      topic = socket.assigns.topic

      # Send a message to the parent to delete the subscription
      send(self(), {:delete_subscription, client_id, topic})

      # Close the modal
      send(self(), {:close_subscription_modal})
    end

    {:noreply, socket}
  end

  @impl true
  def handle_event("save", params, socket) do
    # Extract values from params
    topic = params["topic"]
    qos = params["qos"]
    # Get the index if it exists (for edit mode)
    index = params["index"]

    # Get client_id from params or use pre-selected client_id if available
    client_id = params["client_id"] || socket.assigns.pre_selected_client_id

    # Convert string values to appropriate types
    qos_int = String.to_integer(qos || "0")

    # Check if the selected client supports MQTT 5.0 features
    mqtt5_supported =
      show_mqtt5_options?(%{
        form: %{"client_id" => client_id},
        connected_clients: socket.assigns.connected_clients
      })

    # Handle MQTT 5.0 specific options only if supported
    {nl, rap, rh_int, sub_id_int} =
      if mqtt5_supported do
        # For checkboxes, the value will be "on" when checked and nil when unchecked
        # 将复选框值转换为整数 1 或 0
        nl = if params["nl"] == "on", do: 1, else: 0
        rap = if params["rap"] == "on", do: 1, else: 0
        rh = params["rh"]
        sub_id = params["sub_id"]

        rh_int = String.to_integer(rh || "0")

        # Parse subscription identifier if provided
        sub_id_int =
          case sub_id do
            "" ->
              nil

            nil ->
              nil

            val ->
              case Integer.parse(val) do
                {int_val, ""} when int_val > 0 -> int_val
                _ -> nil
              end
          end

        {nl, rap, rh_int, sub_id_int}
      else
        # For MQTT 3.x, use default values
        {0, 0, 0, nil}
      end

    # Build subscription options
    sub_opts =
      if mqtt5_supported do
        [
          {:qos, qos_int},
          {:rh, rh_int},
          {:nl, nl},
          {:rap, rap}
        ]
      else
        # For MQTT 3.x, only include QoS
        [
          {:qos, qos_int}
        ]
      end

    # Extract subscription identifier to be passed separately
    # It will be handled in the Manager.subscribe function
    id_opt =
      if sub_id_int != nil and sub_id_int > 0 do
        sub_id_int
      else
        nil
      end

    # Check if we have a valid client_id
    if client_id && client_id != "" do
      # If we're in edit mode, first unsubscribe from the old topic
      if socket.assigns.edit_mode do
        # Get the original topic from the socket assigns
        original_topic = socket.assigns.topic

        # If the topic has changed, unsubscribe from the old topic first
        if original_topic != topic do
          Mqttable.MqttClient.Manager.unsubscribe(client_id, original_topic)
        end
      end

      # Send a message to the parent to subscribe to the topic
      # Include the index for edit mode identification
      send(self(), {:subscribe_to_topic, client_id, topic, sub_opts, id_opt, index})

      # Close the modal
      send(self(), {:close_subscription_modal})

      {:noreply, socket}
    else
      # Show an error message if no client is selected
      socket = put_flash(socket, :error, "Please select a client")
      {:noreply, socket}
    end
  end
end
